["generated_tests/test_TC_001_1.py::test_tc_001_step_1", "generated_tests/test_TC_001_1.py::test_tc_001_step_1_login_page_navigation", "generated_tests/test_TC_001_1.py::test_tc_001_step_1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747903841.py::test_tc_001_step_1", "generated_tests/test_TC_001_1_1747906740.py::test_step1_verify", "generated_tests/test_TC_001_1_1747909132_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747910029_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747910782_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747911727_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747911860_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747925778_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747930602_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1747932632_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747933280_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747947204_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747950274_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747952967_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747961013_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1747965352_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_1_1747966408_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748024385_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748380615_merged.py::test_step1_navigation_to_login", "generated_tests/test_TC_001_1_1748383683_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748385949_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748387832_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748393383_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748407539_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_1_1748415497_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748415635_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748416282_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748416962_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748418681_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748422008_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748424170_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748424869_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748426096_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748427089_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748428459_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748443538_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748458623_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748466627_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748468995_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748474809_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748483161_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748483592_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748561275_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748562751_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748567889_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748581785_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748627773_merged.py::test_step1_verify", "generated_tests/test_TC_001_1_1748638159_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748641058_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748641143_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748642233_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748642612_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748643221_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748645047_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748653377_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748669898_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748675069_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748675274_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748709602_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748892297_merged.py::test_step1_navigate", "generated_tests/test_TC_001_1_1748903936_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2.py::test_step_2_verify_valid_userid", "generated_tests/test_TC_001_2_1747934035_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_2_1747934035_merged.py::test_step2_enter_valid_userid[michael981]", "generated_tests/test_TC_001_2_1747947670_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_2_1747947670_merged.py::test_step2_enter_valid_userid[robert196]", "generated_tests/test_TC_001_2_1747950335_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_2_1747950335_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_2_1747953046_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_2_1747953046_merged.py::test_step2_enter_valid_userid[david822]", "generated_tests/test_TC_001_2_1747961083_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1747961083_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_2_1748380702_merged.py::test_step1_navigation_to_login", "generated_tests/test_TC_001_2_1748380702_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_2_1748383743_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1748383743_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_2_1748386025_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1748386025_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_2_1748387883_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1748387883_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_2_1748393576_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1748393576_merged.py::test_step2_enter_userid", "generated_tests/test_TC_001_2_1748407612_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_2_1748407612_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_2_1748417489_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748417489_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748418538_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748418538_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748418738_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748418738_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748420050_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748420050_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748420488_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748420488_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748421062_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748421062_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748422170_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748423223_merged.py::test_complete_workflow", "generated_tests/test_TC_001_2_1748425216_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748425216_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748426227_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748426227_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748427149_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748427149_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748428539_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748428539_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748443678_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748443678_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748458731_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748458731_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748469093_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748469093_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748561366_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748561366_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748562813_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748562813_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748567946_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748567946_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748581849_merged.py::test_step1_verify", "generated_tests/test_TC_001_2_1748581849_merged.py::test_step2_verify", "generated_tests/test_TC_001_2_1748638219_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748638219_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748643280_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748643280_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748645105_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748645105_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748653549_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748653549_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748669984_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748669984_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748675343_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748675343_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748709694_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748709694_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748892431_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748892431_merged.py::test_step2_type", "generated_tests/test_TC_001_2_1748904084_merged.py::test_step1_navigate", "generated_tests/test_TC_001_2_1748904084_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1747934149_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_3_1747934149_merged.py::test_step2_enter_valid_userid[michael981]", "generated_tests/test_TC_001_3_1747934149_merged.py::test_step3_enter_password[test_data0]", "generated_tests/test_TC_001_3_1747950413_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_3_1747950413_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_3_1747950413_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1747953182_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_3_1747953182_merged.py::test_step2_enter_valid_userid[david822]", "generated_tests/test_TC_001_3_1747953182_merged.py::test_step3_enter_password[test_data0]", "generated_tests/test_TC_001_3_1747961156_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1747961156_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_3_1747961156_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748383848_merged.py::test_step1_navigation_to_login", "generated_tests/test_TC_001_3_1748383848_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_3_1748383848_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748384554_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1748384554_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_3_1748384554_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748386107_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1748386107_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_3_1748386107_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748387973_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1748387973_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_3_1748387973_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748393633_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1748393633_merged.py::test_step2_enter_userid", "generated_tests/test_TC_001_3_1748393633_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748407740_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_3_1748407740_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_3_1748407740_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_3_1748421135_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748421135_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748421135_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748423345_merged.py::test_complete_workflow", "generated_tests/test_TC_001_3_1748423402_merged.py::test_complete_workflow", "generated_tests/test_TC_001_3_1748423942_merged.py::test_complete_workflow", "generated_tests/test_TC_001_3_1748425557_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748425557_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748425557_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748426322_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748426322_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748426322_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748426389_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748426389_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748426389_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748426442_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748426442_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748426442_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748427233_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748427233_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748427233_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748427534_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748427534_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748427534_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748427786_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748427786_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748427786_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748428615_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748428615_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748428615_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748443778_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748443778_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748443778_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748458797_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748458797_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748458797_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748469204_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748469204_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748469204_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748561476_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748561476_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748561476_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748562888_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748562888_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748562888_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748568021_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748568021_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748568021_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748581926_merged.py::test_step1_verify", "generated_tests/test_TC_001_3_1748581926_merged.py::test_step2_verify", "generated_tests/test_TC_001_3_1748581926_merged.py::test_step3_verify", "generated_tests/test_TC_001_3_1748638291_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748638291_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748638291_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748643373_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748643373_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748643373_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748645176_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748645176_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748645176_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748653717_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748653717_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748653717_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748670064_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748670064_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748670064_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748675410_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748675410_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748675410_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748709774_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748709774_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748709774_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748892604_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748892604_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748892604_merged.py::test_step3_type", "generated_tests/test_TC_001_3_1748904282_merged.py::test_step1_navigate", "generated_tests/test_TC_001_3_1748904282_merged.py::test_step2_type", "generated_tests/test_TC_001_3_1748904282_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1747934296_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_4_1747934296_merged.py::test_step2_enter_valid_userid[michael981]", "generated_tests/test_TC_001_4_1747934296_merged.py::test_step3_enter_password[test_data0]", "generated_tests/test_TC_001_4_1747934296_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1747950473_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_4_1747950473_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_4_1747950473_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1747950473_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1747953294_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_001_4_1747953294_merged.py::test_step2_enter_valid_userid[david822]", "generated_tests/test_TC_001_4_1747953294_merged.py::test_step3_enter_password[test_data0]", "generated_tests/test_TC_001_4_1747953294_merged.py::test_step4_click_login", "generated_tests/test_TC_001_4_1747961211_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1747961211_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_4_1747961211_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1747961211_merged.py::test_step4_click_login", "generated_tests/test_TC_001_4_1748383910_merged.py::test_step1_navigation_to_login", "generated_tests/test_TC_001_4_1748383910_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_4_1748383910_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748383910_merged.py::test_step4_click_login", "generated_tests/test_TC_001_4_1748384625_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1748384625_merged.py::test_step2_enter_valid_userid", "generated_tests/test_TC_001_4_1748384625_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748384625_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1748386196_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1748386196_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_4_1748386196_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748386196_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1748388032_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1748388032_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_4_1748388032_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748388032_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1748393700_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1748393700_merged.py::test_step2_enter_userid", "generated_tests/test_TC_001_4_1748393700_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748393700_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1748407808_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_001_4_1748407808_merged.py::test_step2_enter_username", "generated_tests/test_TC_001_4_1748407808_merged.py::test_step3_enter_password", "generated_tests/test_TC_001_4_1748407808_merged.py::test_step4_click_login_button", "generated_tests/test_TC_001_4_1748425626_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748425626_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748425626_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748425626_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748426598_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748426598_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748426598_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748426598_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748427861_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748427861_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748427861_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748427861_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748458872_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748458872_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748458872_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748458872_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748469270_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748469270_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748469270_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748469270_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748561555_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748561555_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748561555_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748561555_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748562980_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748562980_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748562980_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748562980_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748563696_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748563696_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748563696_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748563696_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748564327_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748564327_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748564327_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748564327_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748565500_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748565500_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748565500_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748565500_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748566333_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748566333_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748566333_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748566333_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748566541_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748566541_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748566541_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748566541_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748568087_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748568087_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748568087_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748568087_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748581988_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748581988_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748581988_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748581988_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748583105_merged.py::test_step1_verify", "generated_tests/test_TC_001_4_1748583105_merged.py::test_step2_verify", "generated_tests/test_TC_001_4_1748583105_merged.py::test_step3_verify", "generated_tests/test_TC_001_4_1748583105_merged.py::test_step4_verify", "generated_tests/test_TC_001_4_1748638350_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748638350_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748638350_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748638350_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748638477_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748638477_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748638477_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748638477_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748643496_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748643496_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748643496_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748643496_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748643576_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748643576_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748643576_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748643576_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748643668_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748643668_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748643668_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748643668_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748645329_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748645329_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748645329_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748645329_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748653825_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748653825_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748653825_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748653825_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748655147_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748655147_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748655147_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748655147_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748656135_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748656135_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748656135_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748656135_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748657064_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748657064_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748657064_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748657064_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748670151_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748670151_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748670151_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748670151_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748670710_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748670710_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748670710_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748670710_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748671986_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748671986_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748671986_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748671986_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748673798_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748673798_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748673798_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748673798_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748675593_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748675593_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748675593_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748675593_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748709840_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748709840_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748709840_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748709840_merged.py::test_step4_click", "generated_tests/test_TC_001_4_1748904418_merged.py::test_step1_navigate", "generated_tests/test_TC_001_4_1748904418_merged.py::test_step2_type", "generated_tests/test_TC_001_4_1748904418_merged.py::test_step3_type", "generated_tests/test_TC_001_4_1748904418_merged.py::test_step4_click", "generated_tests/test_TC_001_consolidated_1748673859.py::test_full_flow", "generated_tests/test_TC_001_consolidated_1748675643.py::test_full_flow", "generated_tests/test_TC_001_consolidated_1748904474.py::test_full_flow", "generated_tests/test_TC_001_optimized_1748409713.py::test_login_flow", "generated_tests/test_TC_001_optimized_1748427906.py::TestLogin::test_login_success", "generated_tests/test_TC_001_optimized_1748428717.py::test_step1_verify", "generated_tests/test_TC_001_optimized_1748428717.py::test_step2_verify", "generated_tests/test_TC_001_optimized_1748428717.py::test_step3_verify", "generated_tests/test_TC_001_optimized_1748458935.py::test_step1_verify", "generated_tests/test_TC_001_optimized_1748458935.py::test_step2_verify", "generated_tests/test_TC_001_optimized_1748458935.py::test_step3_verify", "generated_tests/test_TC_001_optimized_1748458935.py::test_step4_verify", "generated_tests/test_TC_001_optimized_1748564423.py::test_step1_verify", "generated_tests/test_TC_001_optimized_1748564423.py::test_step2_verify", "generated_tests/test_TC_001_optimized_1748564423.py::test_step3_verify", "generated_tests/test_TC_001_optimized_1748564423.py::test_step4_verify", "generated_tests/test_TC_001_optimized_1748568204.py::test_login", "generated_tests/test_TC_001_optimized_1748582218.py::test_login", "generated_tests/test_TC_001_optimized_1748583179.py::test_login", "generated_tests/test_TC_001_optimized_1748638639.py::test_login_flow", "generated_tests/test_TC_001_optimized_1748645454.py::test_login_flow", "generated_tests/test_TC_002_1_1747953740_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_002_1_1748560710_merged.py::test_step1_verify", "generated_tests/test_TC_002_1_1748585251_merged.py::test_step1_verify", "generated_tests/test_TC_002_2_1747954056_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_002_2_1747954056_merged.py::test_step2_enter_uppercase_userid", "generated_tests/test_TC_002_2_1748585400_merged.py::test_step1_verify", "generated_tests/test_TC_002_2_1748585400_merged.py::test_step2_verify", "generated_tests/test_TC_002_3_1747954132_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_002_3_1747954132_merged.py::test_step2_enter_uppercase_userid", "generated_tests/test_TC_002_3_1747954132_merged.py::test_step3_enter_password", "generated_tests/test_TC_002_3_1748585565_merged.py::test_step1_verify", "generated_tests/test_TC_002_3_1748585565_merged.py::test_step2_verify", "generated_tests/test_TC_002_3_1748585565_merged.py::test_step3_verify", "generated_tests/test_TC_002_4_1747954277_merged.py::test_step1_navigate_to_login_page", "generated_tests/test_TC_002_4_1747954277_merged.py::test_step2_enter_uppercase_userid", "generated_tests/test_TC_002_4_1747954277_merged.py::test_step3_enter_password", "generated_tests/test_TC_002_4_1747954277_merged.py::test_step4_click_login_button", "generated_tests/test_TC_002_4_1748585930_merged.py::test_step1_verify", "generated_tests/test_TC_002_4_1748585930_merged.py::test_step2_verify", "generated_tests/test_TC_002_4_1748585930_merged.py::test_step3_verify", "generated_tests/test_TC_002_4_1748585930_merged.py::test_step4_verify", "generated_tests/test_TC_002_4_1748586076_merged.py::test_step1_verify", "generated_tests/test_TC_002_4_1748586076_merged.py::test_step2_verify", "generated_tests/test_TC_002_4_1748586076_merged.py::test_step3_verify", "generated_tests/test_TC_002_4_1748586076_merged.py::test_step4_verify", "generated_tests/test_TC_002_4_1748587211_merged.py::test_step1_verify", "generated_tests/test_TC_002_4_1748587211_merged.py::test_step2_verify", "generated_tests/test_TC_002_4_1748587211_merged.py::test_step3_verify", "generated_tests/test_TC_002_4_1748587211_merged.py::test_step4_verify", "generated_tests/test_TC_003_1.py::test_tc_003_step_1", "generated_tests/test_TC_003_1_1748392648_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_003_2_1748392724_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_003_2_1748392724_merged.py::test_step2_enter_username", "generated_tests/test_TC_003_3_1748392786_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_003_3_1748392786_merged.py::test_step2_enter_username", "generated_tests/test_TC_003_3_1748392786_merged.py::test_step3_enter_password", "generated_tests/test_TC_003_4_1748392836_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_003_4_1748392836_merged.py::test_step2_enter_username", "generated_tests/test_TC_003_4_1748392836_merged.py::test_step3_enter_password", "generated_tests/test_TC_003_4_1748392836_merged.py::test_step4_click_login_button", "generated_tests/test_TC_004_1_1748324776_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_004_2_1748325198_merged.py::test_step1_navigation_to_login_page", "generated_tests/test_TC_004_2_1748325198_merged.py::test_step2_enter_userid", "generated_tests/test_TC_021_1.py::test_tc_021_step_1", "generated_tests/test_TC_021_1_1748635028_merged.py::test_step1_navigate", "generated_tests/test_TC_021_2_1748635091_merged.py::test_step1_navigate", "generated_tests/test_TC_021_2_1748635091_merged.py::test_step2_type", "generated_tests/test_TC_021_3_1748635216_merged.py::test_step1_navigate", "generated_tests/test_TC_021_3_1748635216_merged.py::test_step2_type", "generated_tests/test_TC_021_3_1748635216_merged.py::test_step3_type", "generated_tests/test_TC_021_3_1748635326_merged.py::test_step1_navigate", "generated_tests/test_TC_021_3_1748635326_merged.py::test_step2_type", "generated_tests/test_TC_021_3_1748635326_merged.py::test_step3_type", "generated_tests/test_TC_021_4_1748635889_merged.py::test_step1_navigate", "generated_tests/test_TC_021_4_1748635889_merged.py::test_step2_type", "generated_tests/test_TC_021_4_1748635889_merged.py::test_step3_type", "generated_tests/test_TC_021_4_1748635889_merged.py::test_step4_click", "generated_tests/test_script_TC_001_step1_20250520_144349.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_144744.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_144932.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_150333.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_153402.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_154534.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_155641.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_172121.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_174126.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_175024.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_175651.py::test_tc_001_step_1", "generated_tests/test_script_TC_001_step1_20250520_192400.py::test_tc_001_step_1_login_page_navigation", "generated_tests/test_script_TC_001_step2_20250520_154203.py::test_tc_001_step_2", "generated_tests/test_single_execution_2jl7o0zy.py::test_execution_counter", "generated_tests/test_single_execution_m67psy_w.py::test_execution_counter", "temp_conftest_test.py::test_browser_fixture_available", "temp_stage10_script_20250602_173656.py::test_tc_001_login_with_valid_credentials", "temp_stage10_script_20250602_173937.py::test_tc_001_login_success", "temp_stage10_script_20250602_174913.py::test_tc_001_login_success", "temp_stage10_script_20250602_182741.py::test_tc_001_login_success", "temp_stage10_script_20250602_183546.py::test_tc_002_uppercase_login", "temp_stage10_script_20250602_184248.py::test_tc_001_login_success", "temp_stage10_script_20250602_185707.py::test_tc_002_uppercase_login", "temp_stage10_script_20250603_175405.py::test_tc_002_uppercase_login", "template_generated_TC_001_from_TC_001_20250531_013052.py::test_tc_001_login_success", "template_generated_TC_001_from_TC_001_20250531_024438.py::test_tc_001_successful_login", "template_generated_TC_001_from_TC_001_20250602_014918.py::test_tc_001_login_functionality", "template_generated_TC_005_from_TC_001_20250531_030334.py::test_tc_005_successful_login_after_two_failed_attempts", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage4_to_stage5_manual_element_selection_with_test_data", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage4_to_stage6_navigation_step_no_test_data", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage5_to_stage6_manual_test_data_saved", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage5_to_stage6_test_data_generated", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage5_to_stage6_test_data_skipped", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage6_to_stage7_script_generated", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage6_to_stage7_script_validated", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage7_to_stage4_error_acknowledged_more_steps_remaining", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage7_to_stage8_all_steps_completed_success", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage7_to_stage8_error_acknowledged_all_steps_done", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage8_to_stage3_manual_return_button", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage8_to_stage3_optimization_completed_successfully", "test_automatic_stage_progression.py::TestAutomaticStageProgression::test_stage8_to_stage3_optimization_with_warnings", "test_conftest_fixture.py::test_basic_navigation", "test_conftest_fixture.py::test_conftest_test_data_fixture", "test_conftest_fixture.py::test_driver_fixture_availability", "test_conftest_fixture.py::test_element_interaction", "test_conftest_fixture.py::test_multiple_page_navigation", "test_conftest_fixture.py::test_screenshot_functionality", "test_conftest_fixture.py::test_wait_functionality", "test_conftest_verification.py::test_conftest_verification", "test_conftest_verification.py::test_simple_assertion", "test_hybrid_editing.py::TestStateManagerIntegration::test_enable_without_step_table", "test_hybrid_editing.py::TestStateManagerIntegration::test_hybrid_editing_enable_disable", "test_hybrid_editing.py::TestStateManagerIntegration::test_manual_step_management", "test_hybrid_editing.py::TestStepMerger::test_basic_merging", "test_hybrid_editing.py::TestStepMerger::test_step_merger_class", "test_hybrid_editing.py::TestStepTemplates::test_custom_step_creation", "test_hybrid_editing.py::TestStepTemplates::test_template_categories", "test_hybrid_editing.py::TestStepTemplates::test_template_creation", "test_hybrid_editing.py::TestStepValidation::test_validation_with_good_steps", "test_hybrid_editing.py::TestStepValidation::test_validation_with_problematic_steps", "test_hybrid_editing.py::test_apply_to_test_case_functionality", "test_hybrid_editing.py::test_end_to_end_workflow", "test_hybrid_editing.py::test_stage_integration", "test_optimized_script.py::test_optimized_example", "test_stage10_button_fix.py::test_stage10_button_execution", "test_stage10_execution.py::test_simple_navigation", "test_stage10_fix.py::test_stage10_execution_fix", "test_stage10_simple.py::test_basic_functionality", "test_validation_demo.py::test_step2_enter_username", "test_verbosity_demo.py::test_complete_workflow", "tests/test_ai_merge.py::test_ai_merge_exception_handling", "tests/test_ai_merge.py::test_ai_merge_fallback_invalid_python", "tests/test_ai_merge.py::test_ai_merge_fallback_missing_imports", "tests/test_ai_merge.py::test_ai_merge_fallback_missing_test", "tests/test_ai_merge.py::test_ai_merge_roundtrip", "tests/test_ai_merge.py::test_ai_merge_success"]