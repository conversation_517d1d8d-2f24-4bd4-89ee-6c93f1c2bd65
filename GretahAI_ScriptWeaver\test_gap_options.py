#!/usr/bin/env python3
"""
Test script for the new gap handling options implementation.
"""

import sys
import os
sys.path.append('.')

def test_gap_handling_functions():
    """Test the gap handling functions can be imported and basic structure is correct."""
    
    try:
        # Test imports
        from ui_components.stage10_components import (
            render_gap_handling_options,
            _generate_targeted_gap_questions,
            _build_targeted_questions_prompt,
            _parse_targeted_questions_response,
            _create_fallback_targeted_questions,
            _render_basic_gap_filling_form
        )
        
        from core.template_prompt_builder import (
            _build_enhanced_gap_analysis_section,
            _build_targeted_gap_section,
            _build_inference_gap_section
        )
        
        print("✅ All imports successful")
        
        # Test basic gap analysis data structure
        test_gap_analysis = {
            'compatibility_score': 75,
            'overall_assessment': 'Template appears compatible with minor gaps',
            'gaps_identified': True,
            'gaps': [
                {
                    'type': 'element_locator',
                    'description': 'Missing button locator for login',
                    'prompt': 'What is the locator for the login button?'
                },
                {
                    'type': 'timeout_value',
                    'description': 'Missing timeout duration',
                    'prompt': 'How long should the script wait?'
                }
            ],
            'analysis_timestamp': '2025-01-11 10:30:00'
        }
        
        # Test targeted questions prompt building
        prompt = _build_targeted_questions_prompt(test_gap_analysis['gaps'])
        assert 'Generate Targeted Questions' in prompt
        assert 'element_locator' in prompt
        assert 'timeout_value' in prompt
        print("✅ Targeted questions prompt building works")
        
        # Test fallback targeted questions
        fallback_questions = _create_fallback_targeted_questions(test_gap_analysis['gaps'])
        assert len(fallback_questions) == 2
        assert fallback_questions[0]['gap_type'] == 'element_locator'
        assert fallback_questions[1]['gap_type'] == 'timeout_value'
        print("✅ Fallback targeted questions creation works")
        
        # Test enhanced gap analysis section building
        # Test inference mode
        inference_section = _build_enhanced_gap_analysis_section(
            test_gap_analysis, None, "inference"
        )
        assert 'Intelligent Inference' in inference_section
        assert 'AI makes smart assumptions' in inference_section
        print("✅ Inference gap section building works")
        
        # Test targeted mode
        test_gap_responses = {
            'gap_0': {
                'type': 'element_locator',
                'description': 'Missing button locator for login',
                'question': 'What is the exact ID or CSS selector for the login button?',
                'response': '#login-btn',
                'input_type': 'text_input'
            },
            'gap_1': {
                'type': 'timeout_value',
                'description': 'Missing timeout duration',
                'question': 'How many seconds should the script wait?',
                'response': '10',
                'input_type': 'number_input'
            }
        }
        
        targeted_section = _build_enhanced_gap_analysis_section(
            test_gap_analysis, test_gap_responses, "targeted"
        )
        assert 'Targeted Gap Filling' in targeted_section
        assert '#login-btn' in targeted_section
        assert '10' in targeted_section
        print("✅ Targeted gap section building works")
        
        print("\n🎉 All tests passed! Gap handling options implementation is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gap_handling_functions()
    sys.exit(0 if success else 1)
